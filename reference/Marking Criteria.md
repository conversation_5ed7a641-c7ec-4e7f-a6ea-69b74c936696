# INFS3822 – AI for Business Analytics
## Marking Criteria for the Individual Hands-on Assignment

---

## Analysis of Structured Data (60 marks)

### Rationale of the Choice of ML Algorithms

**Fail (0% - 49%)**
- Inadequate or no justification for the choice of ML algorithms
- The chosen algorithms are not relevant to the business objective of GlobalStore
- Limited or no explanation of the algorithms' suitability for the task

**Pass (50% - 64%)**
- Basic justification provided for the choice of ML algorithms
- Chosen algorithms are somewhat relevant to the business objective of GlobalStore
- Basic explanation of the algorithms' suitability for the task

**Credit (65%-74%)**
- Clear justification for the choice of ML algorithms
- Chosen algorithms are relevant and generally suitable for the business objective of GlobalStore
- Good explanation of the algorithms' applicability to the task

**Distinction (75%-84%)**
- Comprehensive justification for the choice of ML algorithms
- Chosen algorithms are highly relevant and suitable for the business objective of GlobalStore
- Strong explanation of the algorithms' strengths and limitations

**High Distinction (85% - 100%)**
- Exceptional justification for the choice of ML algorithms
- Chosen algorithms are the most appropriate for the business objective, with clear evidence of critical thinking
- Deep explanation of the algorithms' theoretical and practical implications

### Data Pre-processing and Selection of Key Features

**Fail (0% - 49%)**
- Inadequate or no data pre-processing conducted
- Poor or no handling of missing values, outliers, duplicates, etc.
- No clear justification for the selection of features

**Pass (50% - 64%)**
- Basic data pre-processing conducted
- Somewhat adequate handling of missing values, outliers, duplicates, etc.
- Limited justification for the selection of features

**Credit (65%-74%)**
- Clear and appropriate data pre-processing conducted
- Adequate handling of missing values, outliers, duplicates, etc.
- Justified selection of relevant features

**Distinction (75%-84%)**
- Comprehensive data pre-processing conducted
- Effective handling of missing values, outliers, duplicates, etc.
- Well-justified and relevant feature selection

**High Distinction (85% - 100%)**
- Exceptional data pre-processing conducted
- Excellent handling of all data quality issues
- Excellent justification and well-supported feature selection

### Training the TWO (2) ML Models

**Fail (0% - 49%)**
- No split of training and testing data
- Inadequate or incorrect explanation of the model training process
- Limited understanding of the steps involved
- No Python or R code included

**Pass (50% - 64%)**
- Basic split of training and testing data
- Basic explanation of the model training process
- Basic understanding of the steps involved
- Basic code provided
- Python or R code included, with extensive errors

**Credit (65%-74%)**
- Adequate split of training and testing data
- Clear explanation of the model training process
- Good understanding of the steps involved
- Correct and functional code provided
- Python or R code included, with some errors

**Distinction (75%-84%)**
- Good split of training and testing data
- Comprehensive explanation of the model training process
- Strong understanding of the steps involved
- Well-documented and functional code provided
- Good Python or R code included without errors

**High Distinction (85% - 100%)**
- Excellent split of training and testing data
- Exceptional explanation of the model training process with critical insights
- Deep understanding of the training steps and their implications
- Highly efficient and well-documented code provided
- Excellent Python or R code included without errors

### Testing and Evaluating the TWO (2) ML Models

**Fail (0% - 49%)**
- Inadequate or incorrect explanation of the model testing process
- Limited or no evaluation of model performance
- Limited or no comparison of the models' performance
- Wrong or no selection of the best performing model
- No Python or R code included

**Pass (50% - 64%)**
- Basic explanation of the model testing process
- Basic evaluation of model performance
- Basic comparison of the models' performance
- The best performing model is selected but lacks justification
- Python or R code included, with extensive errors

**Credit (65%-74%)**
- Clear explanation of the model testing process
- Good evaluation of model performance
- Good comparison of the models' performance
- The best performing model is selected with limited justification
- Python or R code included, with some errors

**Distinction (75%-84%)**
- Comprehensive explanation of the model testing process
- Excellent evaluation of model performance
- Excellent comparison of the models' performance
- The best performing model is selected with good justification
- Good Python or R code included without errors

**High Distinction (85% - 100%)**
- Outstanding explanation of the model testing process with critical insights
- Outstanding evaluation of model performance
- Outstanding comparison of the models' performance
- The best performing model is selected with excellent justification
- Excellent Python or R code included without errors

### Discuss the Findings of Your Analysis and Derive Actionable Insights

**Fail (0% - 49%)**
- Inadequate or incorrect discussion of findings
- Limited or no actionable insights
- No clear recommendations provided

**Pass (50% - 64%)**
- Basic discussion of findings with no plausible explanations
- Basic actionable insights
- Some recommendations provided

**Credit (65%-74%)**
- Clear discussion of findings with good explanations
- Relevant actionable insights
- Well-supported recommendations provided

**Distinction (75%-84%)**
- Comprehensive discussion of findings with strong plausible explanations
- Highly relevant actionable insights
- Strong, well-supported recommendations

**High Distinction (85% - 100%)**
- Exceptional discussion of findings with excellent plausible explanations
- Highly relevant and impactful actionable insights
- Insightful and highly effective recommendations

---

## Analysis of Unstructured Data (40 marks)

### Rationale of the Choice of ML Algorithm

**Fail (0% - 49%)**
- Inadequate or no justification for the choice of the ML algorithm
- The chosen algorithm is not relevant to the business objective
- Limited or no understanding of the algorithm's suitability for the task

**Pass (50% - 64%)**
- Basic justification provided for the choice of the ML algorithm
- The chosen algorithm is somewhat relevant to the business objective
- Basic understanding of the algorithm's suitability for the task

**Credit (65%-74%)**
- Clear justification for the choice of the ML algorithm
- The chosen algorithm is relevant and generally suitable for the business objective
- Good understanding of the algorithm's applicability to the task

**Distinction (75%-84%)**
- Comprehensive justification for the choice of the ML algorithm
- The chosen algorithm is highly relevant and suitable for the business objective
- Strong understanding of the algorithm's strengths and limitations

**High Distinction (85% - 100%)**
- Exceptional justification for the choice of the ML algorithm
- The chosen algorithm is the most appropriate for the business objective, with clear evidence of critical thinking
- Deep understanding of the algorithm's theoretical and practical implications

### Data Transformation (Feature Extraction) and Selection of Model Parameters

**Fail (0% - 49%)**
- Inadequate or incorrect feature extraction methods
- Poor selection or justification of model parameters
- No clear discussion of the findings

**Pass (50% - 64%)**
- Basic feature extraction methods implemented with limited justifications
- Somewhat adequate selection of model parameters with limited justification
- Basic discussion of the findings

**Credit (65%-74%)**
- Appropriate feature extraction methods with good justifications
- Adequate selection and justification of model parameters
- Good discussion of the findings

**Distinction (75%-84%)**
- Comprehensive feature extraction methods with strong justifications
- Well-chosen and justified model parameters
- Strong discussion of the findings

**High Distinction (85% - 100%)**
- Exceptional feature extraction methods with excellent justifications
- Insightful and well-supported selection of model parameters
- Highly detailed and insightful discussion of the findings

### Implementation of the ML Model

**Fail (0% - 49%)**
- Inadequate or incorrect explanation of the model implementation
- Limited understanding of the implementation steps
- No Python or R code included

**Pass (50% - 64%)**
- Basic explanation of the model implementation
- Basic understanding of the implementation steps
- Python or R code included, with extensive errors

**Credit (65%-74%)**
- Clear explanation of the model implementation
- Good understanding of the implementation steps
- Python or R code included, with some errors

**Distinction (75%-84%)**
- Comprehensive explanation of the model implementation
- Strong understanding of the implementation steps
- Good Python or R code included without errors

**High Distinction (85% - 100%)**
- Exceptional explanation of the model implementation
- Deep understanding of the implementation steps
- Excellent Python or R code included without errors

### Discuss the Key Findings of Your Analysis and Derive Actionable Insights

**Fail (0% - 49%)**
- Inadequate or incorrect discussion of findings
- Limited or no actionable insights
- No clear recommendations provided

**Pass (50% - 64%)**
- Basic discussion of findings with no plausible explanations
- Basic actionable insights
- Some recommendations provided

**Credit (65%-74%)**
- Clear discussion of findings with good explanations
- Relevant actionable insights
- Well-supported recommendations provided

**Distinction (75%-84%)**
- Comprehensive discussion of findings with strong plausible explanations
- Highly relevant actionable insights
- Strong, well-supported recommendations

**High Distinction (85% - 100%)**
- Exceptional discussion of findings with excellent plausible explanations
- Highly relevant and impactful actionable insights
- Insightful and highly effective recommendations

---

## Writing and Structure of Report

**Fail (0% - 49%)**
- Your writing is not professional in tone and has major spelling and grammatical errors
- Your written expression does not indicate a logic/flow between each section of the report
- Poor or unclear structure
- Your sources have not been referenced, and/or there are excessive errors in referencing in the report
- The word limit has not been adhered to
- No executive summary is provided

**Pass (50% - 64%)**
- Some attempt has been made to use a professional tone and presentation in your writing, but there are some spelling and grammatical errors
- You have endeavoured to provide logic/flow between each section of the report
- Attempt to a good structure but lack coherent flow between sections
- Some sources are referenced throughout the report, but there are errors in your referencing of sources
- An executive summary is provided but missing key aspects of the report

**Credit (65%-74%)**
- Your writing is mostly professional in tone and presentation, but occasional spelling and/or grammatical errors exist
- Your written expression indicates the logic/flow between each section of the report
- Good structure with organised headings
- Most sources are referenced throughout the report, with only minor errors in referencing
- An executive summary is provided and covers essential aspects of the report

**Distinction (75%-84%)**
- Your writing is professional in tone and presentation, with a few very minor spellings and/or grammatical errors
- Your written expression strongly indicates the logic/flow between each section of the report
- Good structure with organised headings and coherent follow between sections
- All sources are referenced throughout the report with only minor errors in referencing
- An executive summary is provided and covers essential aspects of the report using non-jargon language

**High Distinction (85% - 100%)**
- Your writing is professional in tone and presented outstandingly with no spelling or grammatical errors
- Your written expression provides a strong and coherent indication of the logic/flow between each section, enabling key arguments to develop fully
- Good structure with organised headings and coherent follow between sections
- All sources are referenced throughout the report, and the seeds are used very well, with no significant errors in referencing
- A concise executive summary is provided and covers essential aspects of the report using jargon-free language