1a - Rationale of the choice of ML algorithms

1a-CHOICE1: Justification of choice of first ML algorithm (5 mark)
1a-CHOICE2: Justification of choice of second ML algorithm (5 mark)

Clever choice of the Logistic Regression algorithm. You have provided a good discussion of the reasons behind the choice of the Logistic Regression algorithm. 

Your answer could be improved if you elaborate further on why the Logistic Regression is suitable for a binary target variable like “returned”.

You have provided a good discussion of the reasons behind the choice of the Decision Tree algorithm. 

Your answer could be improved if you elaborate further on why Logistic Regression is suitable for a binary target variable like "returned." This algorithm is specifically designed for binary classification problems, predicting the outcome (Target Variable) based on one or more predictor variables. It effectively estimates the probability of returns and demonstrates the influence of each predictor on the likelihood of a product being returned.

Clever choice of Decision Tree algorithm. Your answer could be improved if you elaborate further on the rationale of why Decision Tree is suitable for a classification problem, given that the target variable “returned” is a 2-class binary variable (Yes, No).

It would be great if you could provide a more specific answer. Consider the Target Variable, which is a 2-class binary variable (Yes, No) and articulate why the Decision Tree algorithm is suitable for this type of ML problem.

Clever choice of Random Forest algorithm! Your answer could be improved if you elaborate further on why Random Forest is suitable for a classification problem, given that the target variable “returned” is a 2-class binary variable (Yes, No).

Good choice of Neural Network algorithm. Your answer could be improved if you have discussed further why Neural Network is suitable for a classification problem, given that the target variable “returned” is a 2-class binary variable (Yes, No).

You have provided an excellent discussion of the reasons behind the choice of Logistic Regression algorithm along with justifications to support your arguments. Well done!

You have provided an excellent discussion of the rationale behind the choice of Decision Tree algorithm. 

You have also provided justifications to support your arguments. Well done!

You have provided a great discussion of the reasons behind the choice of Random Forest algorithm. 
You have provided justifications of your arguments. Well done!
Excellent justification of your choice of Neural Network algorithm. Well done!

Your answer lacks a discussion of the reasons behind the choice of Decision Tree algorithm.

Your answer lacks a discussion of the reasons behind the choice of Logistic Regression algorithm.

Your answer lacks a discussion of the reasons behind the choice of Neural network algorithm.

You have provided an excellent discussion of the rationale behind the choice of the ML algorithms. Well done!

1b - Data pre-processing and selection of the key features

1b-LOAD: Loading the data from a csv file (1 mark)

Your answer could be improved if you articulate how you loaded the data from a CSV file in Python or R, and how this does not automatically identify whether variables are dates or categories, thus necessitating data transformation.

Your answer lacks a discussion of how you loaded the data from a CSV file in Python or R, and how this does not automatically identify whether variables are dates or categories, thus necessitating data transformation.

Please check the code, most students write the code for loading the data, but they forget to discuss it. Please assign half of the mark.

1b-CLEAN: Cleaning the data (1 mark)

You have provided a good explanation of data cleaning. Your answer could be improved if you discussed the results of the cleaning steps in more detail. For example, it would be great if you could clarify if the data have missing values? How many rows of data contain missing values? Are there any duplicates in the data? Providing these details would make your answer comprehensive. 

Your discussion of data cleaning is quite brief. Your answer could be improved if you discussed the results of the cleaning steps in more detail. For example, it would be great if you could clarify if the data have missing values? How many rows of data contain missing values? Are there any duplicates in the data? Providing these details would make your answer comprehensive.

Clever choice of removing outliers. You have provided a great discussion of how you handled outliers.

You have provided a great discussion of data cleaning. Well done!

You have provided a brief explanation of data cleaning. Your answer could be improved if you discussed the results of the cleaning steps in more detail. For example, it would be great if you could clarify if the data have missing values? How many rows of data contain missing values? Are there any duplicates in the data? Providing these details would make your answer comprehensive.

 
Your work lacks a discussion of the data cleaning.


1b-TRANSFORM: Transform the data (3 mark)

It would be great if you consider turning date data (e.g. order_date) into a categorical variable as part of the data transformation. For example, certain times of the year, such as Christmas, Valentine’s Day, New Year Eve, and Halloween, etc, correspond to peaks in purchase behaviour. The likelihood of product returns might increase during these periods, making it important to account for seasonal trends in your analysis.

While your code includes data transformation steps such as converting the target variable to a binary variable and converting categorical variables to dummy variables, your answer could be improved if you discussed those data transformations steps in your report.

You have provided a good discussion of the data transformation. Your answer could be improved if you justify each transformation and explain how it enhances model performance.

It would be great if you could discuss data transformation, justify each transformation and explain how it enhances model performance.

Code for data transformation is not provided.

You have provided an excellent discussion of the data transformation. Well done!

Great to see that you leveraged date transformation. It would be great if you consider turning date data (e.g. order_date) into a categorical variable as part of the data transformation. For example, certain times of the year, such as Christmas, Valentine’s Day, New Year Eve, and Halloween, etc, correspond to peaks in purchase behaviour. The likelihood of product returns might increase during these periods, making it important to account for seasonal trends in your analysis.

Great to see that you leveraged order_date transformation in your code. Your answer could be improved if you justify each transformation and explain how it enhances model performance. 

1b-SCALE: Scale numeric values (1 mark)

It would be great if you could consider scaling numeric values in the data as part of the pre-processing step, before training the model to improve its performance. Your answer could have also been enhanced if you elaborate further on why scaling step is necessary before training a machine learning model to improve the performance of the predictive models.

You have discussed well the scaling of numeric values. Your answer could be improved if you provide a justification of why scaling is necessary. For example, scaling numeric values in the data before training a machine learning model is crucial because it standardises the data, contributing to improve the model performance.

You discussed the scaling of numeric values and provided a good justification for the importance of data standardisation. Well done!

Your discussion of data scaling is too brief. Please elaborate further on why you did the scaling and articulate how this would improve the models' performance.

Please check the code, most students write the code for scaling, but they do not explain why scaling is important and how they did the scaling, please assign half of the mark.


1b-SELECT: Feature Selection (3 mark)

You have provided a good discussion of feature selection. Your answer could be improved if you provide the reasons behind the inclusion and exclusion of variables in the model. For example, some variables are excluded because they have too many possible values to be useful categorical variables. Other variables are excluded because they are highly correlated with other predictors, which could lead to multicollinearity issues in the analysis.

Some variables excluded from your models could be included as predictors. For instance, you excluded order_date and shipping_date from your analysis. It would be great if you could transform order_date into a categorical variable. For example, certain times of the year, such as Christmas, Valentine’s Day, New Year Eve, and Halloween, etc correspond to peaks in purchase behaviour. The likelihood of product returns might also increase during specific periods of the year.

Great to see that you use a correlation matrix heatmap to help with feature selection. Your answer could be improved if you provide further interpretation of the results and support your choices of features to exclude/include.

You have provided a great discussion of the feature selection. Well done!

1b-SPLIT: Split the data into training and testing sets (1 mark)

While you did split the data into training and testing sets, your answer could be improved if you discuss the chosen proportions (e.g. 80% for training and 20% for testing) and the rationale behind this choice. The split ensures a sufficiently large training dataset for effective model learning, while maintaining a reasonable size for the testing dataset to accurately evaluate the model’s performance, thus achieving a balance between training and testing.

Great to see that you split the data into training and testing sets. Your answer could be improved if you discuss the chosen proportions (e.g. 80% for training and 20% for testing) and the rationale behind this choice. The split ensures a sufficiently large training dataset for effective model learning, while maintaining a reasonable size for the testing dataset to accurately evaluate the model’s performance, thus achieving a balance between training and testing.

You have provided an excellent discussion of the splitting of the data into training and testing datasets. Well done!

Your answer lacks a discussion of the splitting of the data into training and testing datasets.

1c - Training the TWO (2) ML models

1c-TRAIN1: Training the first model (5 mark)
1c-TRAIN2: Training the second model (5 mark)

You have provided a good discussion of the training of your model. To enhance your answer, consider elaborating further on the outputs, such as coefficients (for logistic regressions), splits (for decision trees). 

In addition, you should discuss the training process in more detail, including the model’s performance across epochs for neural networks, etc.

Great to see that you enabled Class Weight Adjustment in the training of your model. Your answer could be improved by explaining further why Class Weight Adjustment is needed. The dataset is imbalanced because the number of returned products is much smaller than the number of products that are not returned. Adjusting class weights in your model training helps to assign more importance to the minority class (returned).

The training of your model could be improved by enabling Class Weight Adjustment to account for imbalanced data. The dataset is imbalanced because the number of returned products is much smaller than the number of products that are not returned. Adjusting class weights in your model training helps to assign more importance to the minority class (returned).

You have provided a great discussion of the training of your model, and outputs of your model. Well done!

1d - Testing and evaluating the TWO (2) ML models

1d-TEST1: Testing and evaluating the first model (5 mark)
1d-TEST2: Testing and evaluating the second model (5 mark)

You have provided a good discussion of the testing and evaluation of your models. Your answer could be improved if you elaborate further on the testing and evaluation process. You should explain what dataset was used for testing and why it was chosen (e.g. to test the model’s performance on unseen data). In addition, you should discuss the output of the predictions (e.g. probabilities), any necessary transformations of the output (converting probabilities into a yes/no predictions) and provide a discussion of the confusion matrix and the key metrics (e.g. accuracy, precision, recall, F1 score). Explain the significance of each metric and why certain metrics are particularly important for the evaluation of the model.

You have provided an excellent discussion of the testing and evaluation of your models. Well done!

1d-COMPARE: Compare the performance of the two models (5 mark)

You have provided a good discussion of the models’ comparison. Your answer could be improved if you elaborate further on the reasons behind the selection of the metrics for comparison and how they relate to the business objective. For example, the F1-score offers a good balance between precision and recall, making it an effective metric for comparing classification models.

Please elaborate further on the results of the metrics (Accuracy, F1 score, Precision, Recall) scores to assess the performance of the models in making accurate predictions. For more details, please refer to the answers to the Week 3 and Week 4 Hands-on activities.  

You have provided an excellent discussion of the comparison of the models’ performance. Well done!

1e - Discuss the findings of your analysis and derive actionable insights

1e-FINDINGS: Discuss the findings (7 mark)

You have provided an excellent discussion of the findings of the most important features significantly influencing the target variable.

You have discussed the findings of the most important features significantly impacting the prediction of the target variable. Your answer could be improved if you elaborate further on the strength and direction (whether positive or negative) of the effect each relevant feature has on predicting the target variable. This will provide a clearer understanding of how and to what extent each feature influences the predictions.

You have provided a great discussion of the findings. Well done!

Please provide plausible explanations for these findings to demonstrate your critical thinking.

It would be great if you could provide plausible explanations for the findings.

1e-INSIGHTS: Derive actionable insights from the key findings (5 mark)

You have provided a good discussion of the actionable insights derived from the findings. Your answer could be improved if you elaborate further on how these insights could be translated into actionable recommendations to support decision making. This will guide GlobalStore in taking the best course of action to reduce product returns.

For example, if the findings indicate that a specific product segment is associated with an increase (or decrease) in the likelihood of product return, this demonstrates strengths or weaknesses of the current supply chain and product quality within that segment. This may necessitate strategic adjustments to address issues or take advantage of successful areas.

You have provided an excellent discussion of the actionable insights. Well done!

Please elaborate further on the actionable insights derived from the discussion of the findings of the important features that significantly impact the target variable.

Your work lacks a discussion of the actionable insights. It would be great if you derive insights aligned with the findings and translate them onto actionable recommendations to recommend GlobalStore on the best course of action to reduce product returns.

1e-RECOMMEND: Translate insights into recommendations (3 mark)

You have provided good recommendations. Your answer could be improved if you articulate further how the proposed recommendations are derived from the findings.  It would be great if you could elaborate further on how these recommendations support decision making to reduce product returns.

You have provided excellent recommendations. Well done!


