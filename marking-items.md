# Marking Items

## 1

1a-CHOICE1: Justification of choice of first ML algorithm (5 marks)
1a-CHOICE2: Justification of choice of second ML algorithm (5 mark)
1b-LOAD: Loading the data from a csv file (1 mark)
1b-CLEAN: Cleaning the data (1 mark)
1b-TRANSFORM: Transform the data (3 mark)
1b-SCALE: Scale numeric values (1 mark)
1b-SELECT: Feature Selection (3 mark)
1b-SPLIT: Split the data into training and testing sets (1 mark)
1c-TRAIN1: Training the first model (5 mark)
1c-TRAIN2: Training the second model (5 mark)
1d-TEST1: Testing and evaluating the first model (5 mark)
1d-TEST2: Testing and evaluating the second model (5 mark)
1d-COMPARE: Compare the performance of the two models (5 mark)
1e-FINDINGS: Discuss the findings (7 mark)
1e-INSIGHTS: Derive actionable insights from the key findings (5 mark)
1e-RECOMMEND: Translate insights into recommendations (3 mark)

## 2
2a-RATIONALE: Justification of choice of ML algorithm (10 mark)
2b-LOAD: Loading the data from the images (3 mark)
2b-EXTRACTION: Feature Extraction (3 mark)